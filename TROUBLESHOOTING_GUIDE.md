# Medical Device Management System - Troubleshooting Guide

## Problem: Cannot Add, Edit, or Delete Data

### Root Cause Analysis
The primary issue preventing CRUD operations is that **P<PERSON> is not properly installed or configured** on your Windows system.

### Symptoms
- Forms appear to work but nothing happens when submitted
- No data is saved to the database
- No error messages are displayed
- Pages may redirect but no changes occur

## Solutions

### Option 1: Install XAMPP (Recommended)

1. **Download XAMPP**
   - Go to https://www.apachefriends.org/
   - Download the latest version for Windows
   - Choose the version with PHP 8.0 or higher

2. **Install XAMPP**
   - Run the installer as Administrator
   - Select Apache, MySQL, and PHP components
   - Install to default location (C:\xampp)

3. **Start Services**
   - Open XAMPP Control Panel
   - Start Apache and MySQL services
   - Ensure both show "Running" status

4. **Move Your Project**
   - Copy your entire project folder to `C:\xampp\htdocs\bio`
   - Your project structure should be:
     ```
     C:\xampp\htdocs\bio\
     ├── index.php
     ├── config/
     ├── models/
     ├── controllers/
     ├── views/
     └── ...
     ```

5. **Setup Database**
   - Open http://localhost/phpmyadmin in your browser
   - Create a new database named `medical_device_management`
   - Import the schema from `database/schema.sql`

6. **Test Your Application**
   - Open http://localhost/bio in your browser
   - Login with: username `admin`, password `admin123`

### Option 2: Install WAMP

1. **Download WAMP**
   - Go to http://www.wampserver.com/
   - Download the latest version

2. **Install and Configure**
   - Follow similar steps as XAMPP
   - Move project to `C:\wamp64\www\bio`
   - Access via http://localhost/bio

### Option 3: Use Existing PHP Installation

If PHP is already installed but not working:

1. **Check PHP Installation**
   - Open Command Prompt as Administrator
   - Run: `php --version`
   - If not found, PHP is not in your system PATH

2. **Add PHP to PATH**
   - Find your PHP installation directory
   - Add it to Windows PATH environment variable
   - Restart Command Prompt and test again

## Testing Your Setup

### Step 1: Basic Web Server Test
1. Open your browser
2. Navigate to http://localhost/bio/test.html
3. If you can see the test page, your web server is working

### Step 2: PHP Test
1. On the test page, click "Test PHP"
2. If successful, PHP is working correctly
3. If failed, PHP is not configured properly

### Step 3: Database Test
1. Click "Test Database" on the test page
2. Check if all required tables exist
3. Verify database connectivity

### Step 4: CRUD Test
1. Click "Test CRUD Operations"
2. This will test create, read, update, and delete operations
3. All tests should pass for full functionality

## Common Issues and Fixes

### Issue 1: "PHP not found" Error
**Cause**: PHP is not installed or not in PATH
**Solution**: Install XAMPP or add PHP to system PATH

### Issue 2: Database Connection Failed
**Cause**: MySQL not running or wrong credentials
**Solutions**:
- Start MySQL service in XAMPP/WAMP
- Check database credentials in `config/database.php`
- Ensure database exists

### Issue 3: Permission Denied Errors
**Cause**: User doesn't have required permissions
**Solutions**:
- Login as admin user
- Check user roles and permissions
- Verify session is working

### Issue 4: CSRF Token Errors
**Cause**: Session issues or token validation problems
**Solutions**:
- Clear browser cache and cookies
- Restart web server
- Check session configuration

### Issue 5: Forms Submit but Nothing Happens
**Cause**: PHP not processing forms
**Solutions**:
- Verify PHP is working (run test.html)
- Check web server error logs
- Ensure form action URLs are correct

## File Permissions

Ensure these directories are writable:
- `uploads/` - For file uploads
- `uploads/qrcodes/` - For QR code images

On Windows with XAMPP, these should work automatically.

## Database Requirements

Required tables:
- users
- hospitals
- departments
- devices
- maintenance_schedules
- tickets
- notifications
- activity_logs

If any tables are missing, re-import `database/schema.sql`

## Getting Help

If you're still having issues:

1. **Run the diagnostic tests**:
   - Open http://localhost/bio/test.html
   - Run all tests and note which ones fail

2. **Check error logs**:
   - XAMPP logs: `C:\xampp\apache\logs\error.log`
   - PHP errors: `C:\xampp\php\logs\php_error_log`

3. **Verify your setup**:
   - PHP version 7.4 or higher
   - MySQL 5.7 or higher
   - All required PHP extensions enabled

## Quick Checklist

- [ ] XAMPP/WAMP installed and running
- [ ] Apache service started
- [ ] MySQL service started
- [ ] Project in correct directory
- [ ] Database created and imported
- [ ] Can access http://localhost/bio
- [ ] Can login with admin credentials
- [ ] All diagnostic tests pass

If all items are checked, your CRUD operations should work correctly.
