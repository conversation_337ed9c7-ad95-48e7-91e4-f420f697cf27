<?php
/**
 * Database Installation Script
 *
 * This script creates the database and tables for the Medical Device Management System.
 * Version: 3.0 - Fixed logic errors and improved error handling
 */

// Display errors for debugging
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// Set content type and security headers
header('Content-Type: text/html; charset=utf-8');
header('X-Content-Type-Options: nosniff');
header('X-Frame-Options: DENY');

// Check if the script is being run from the command line
$isCli = php_sapi_name() === 'cli';

// Function to output messages
function output($message, $isError = false) {
    global $isCli;
    
    if ($isCli) {
        echo ($isError ? "ERROR: " : "") . $message . PHP_EOL;
    } else {
        echo ($isError ? '<div style="color: red; font-weight: bold;">ERROR: ' : '<div>') . $message . '</div>';
    }
}

// Function to get database configuration
function getDatabaseConfig() {
    // Check if config file exists
    if (!file_exists('../config/database.php')) {
        output("Database configuration file not found.", true);
        exit(1);
    }
    
    // Include database configuration
    include '../config/database.php';
    
    // Return database configuration
    return [
        'host' => DB_HOST,
        'name' => DB_NAME,
        'user' => DB_USER,
        'pass' => DB_PASS,
        'charset' => DB_CHARSET
    ];
}

// Function to create database
function createDatabase($config) {
    try {
        // Connect to MySQL without selecting a database
        $pdo = new PDO(
            "mysql:host={$config['host']};charset={$config['charset']}",
            $config['user'],
            $config['pass'],
            [
                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                PDO::ATTR_EMULATE_PREPARES => false
            ]
        );
        
        // Check if database exists
        $stmt = $pdo->query("SELECT SCHEMA_NAME FROM INFORMATION_SCHEMA.SCHEMATA WHERE SCHEMA_NAME = '{$config['name']}'");
        $databaseExists = $stmt->fetchColumn();
        
        if ($databaseExists) {
            output("Database '{$config['name']}' already exists. Dropping and recreating...");
            $pdo->exec("DROP DATABASE `{$config['name']}`");
        }
        
        // Create database
        $pdo->exec("CREATE DATABASE `{$config['name']}` CHARACTER SET {$config['charset']} COLLATE {$config['charset']}_unicode_ci");
        
        output("Database '{$config['name']}' created successfully.");
        
        return true;
    } catch (PDOException $e) {
        output("Failed to create database: " . $e->getMessage(), true);
        return false;
    }
}

// Function to import schema
function importSchema($config) {
    try {
        // Connect to the database
        $pdo = new PDO(
            "mysql:host={$config['host']};dbname={$config['name']};charset={$config['charset']}",
            $config['user'],
            $config['pass'],
            [
                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                PDO::ATTR_EMULATE_PREPARES => false
            ]
        );

        // Read schema file
        $schemaFile = 'schema.sql';
        if (!file_exists($schemaFile)) {
            output("Schema file not found.", true);
            return false;
        }

        $sql = file_get_contents($schemaFile);

        // Remove comments and split SQL by semicolon more intelligently
        $sql = preg_replace('/--.*$/m', '', $sql); // Remove single-line comments
        $sql = preg_replace('/\/\*.*?\*\//s', '', $sql); // Remove multi-line comments

        // Split by semicolon but ignore semicolons in strings
        $queries = [];
        $current = '';
        $inString = false;
        $stringChar = '';

        for ($i = 0; $i < strlen($sql); $i++) {
            $char = $sql[$i];

            if (!$inString && ($char === '"' || $char === "'")) {
                $inString = true;
                $stringChar = $char;
            } elseif ($inString && $char === $stringChar) {
                $inString = false;
                $stringChar = '';
            } elseif (!$inString && $char === ';') {
                $queries[] = trim($current);
                $current = '';
                continue;
            }

            $current .= $char;
        }

        if (trim($current)) {
            $queries[] = trim($current);
        }

        // Execute each query
        foreach ($queries as $query) {
            $query = trim($query);
            if (!empty($query) && !preg_match('/^(USE|DROP DATABASE|CREATE DATABASE)/i', $query)) {
                try {
                    $pdo->exec($query);
                } catch (PDOException $e) {
                    output("Query failed: " . substr($query, 0, 100) . "... Error: " . $e->getMessage(), true);
                    // Continue with other queries instead of failing completely
                }
            }
        }

        output("Schema imported successfully.");

        return true;
    } catch (PDOException $e) {
        output("Failed to import schema: " . $e->getMessage(), true);
        return false;
    }
}

// Function to insert sample data
function insertSampleData($config) {
    try {
        // Connect to the database
        $pdo = new PDO(
            "mysql:host={$config['host']};dbname={$config['name']};charset={$config['charset']}",
            $config['user'],
            $config['pass'],
            [
                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                PDO::ATTR_EMULATE_PREPARES => false
            ]
        );
        
        // Insert sample hospital
        $stmt = $pdo->prepare("
            INSERT INTO hospitals (name, address, city, country, phone, email, website)
            VALUES (?, ?, ?, ?, ?, ?, ?)
        ");
        $stmt->execute([
            'General Hospital',
            '123 Main Street',
            'New York',
            'United States',
            '************',
            '<EMAIL>',
            'https://www.generalhospital.com'
        ]);
        $hospitalId = $pdo->lastInsertId();
        
        // Insert sample departments
        $departments = [
            ['Cardiology', 'Floor 2', '************', '<EMAIL>'],
            ['Radiology', 'Floor 1', '************', '<EMAIL>'],
            ['Emergency', 'Ground Floor', '************', '<EMAIL>']
        ];
        
        $stmt = $pdo->prepare("
            INSERT INTO departments (hospital_id, name, location, phone, email)
            VALUES (?, ?, ?, ?, ?)
        ");
        
        foreach ($departments as $department) {
            $stmt->execute([
                $hospitalId,
                $department[0],
                $department[1],
                $department[2],
                $department[3]
            ]);
            $departmentIds[] = $pdo->lastInsertId();
        }
        
        // Insert sample users
        $users = [
            ['engineer', 'engineer123', '<EMAIL>', 'John Engineer', 'engineer'],
            ['technician', 'technician123', '<EMAIL>', 'Jane Technician', 'technician'],
            ['staff', 'staff123', '<EMAIL>', 'Bob Staff', 'staff']
        ];
        
        $stmt = $pdo->prepare("
            INSERT INTO users (username, password, email, full_name, role, hospital_id)
            VALUES (?, ?, ?, ?, ?, ?)
        ");
        
        foreach ($users as $user) {
            $stmt->execute([
                $user[0],
                password_hash($user[1], PASSWORD_DEFAULT),
                $user[2],
                $user[3],
                $user[4],
                $hospitalId
            ]);
        }
        
        // Insert sample devices
        $devices = [
            ['ECG Machine', 'ECG-2000', 'SN12345', 'CardioTech', 'Cardiology', $departmentIds[0]],
            ['X-Ray Machine', 'XR-5000', 'SN67890', 'RadiologyTech', 'Radiology', $departmentIds[1]],
            ['Ventilator', 'V-1000', 'SN24680', 'MedTech', 'Emergency', $departmentIds[2]]
        ];
        
        $stmt = $pdo->prepare("
            INSERT INTO devices (
                hospital_id, department_id, name, model, serial_number, manufacturer,
                category, purchase_date, warranty_expiry, status
            )
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        ");
        
        foreach ($devices as $device) {
            $stmt->execute([
                $hospitalId,
                $device[5],
                $device[0],
                $device[1],
                $device[2],
                $device[3],
                $device[4],
                date('Y-m-d', strtotime('-1 year')),
                date('Y-m-d', strtotime('+2 years')),
                'operational'
            ]);
        }
        
        output("Sample data inserted successfully.");
        
        return true;
    } catch (PDOException $e) {
        output("Failed to insert sample data: " . $e->getMessage(), true);
        return false;
    }
}

// Main execution
if (!$isCli) {
    echo '<!DOCTYPE html>
    <html>
    <head>
        <title>Database Installation</title>
        <style>
            body {
                font-family: Arial, sans-serif;
                margin: 20px;
                line-height: 1.6;
            }
            h1 {
                color: #333;
            }
            div {
                margin-bottom: 10px;
            }
            .success {
                color: green;
            }
            .error {
                color: red;
                font-weight: bold;
            }
        </style>
    </head>
    <body>
        <h1>Medical Device Management System - Database Installation</h1>';
}

output("Starting database installation...");

// Get database configuration
$config = getDatabaseConfig();

// Create database
if (!createDatabase($config)) {
    output("Installation failed.", true);
    exit(1);
}

// Import schema
if (!importSchema($config)) {
    output("Installation failed.", true);
    exit(1);
}

// Insert sample data
if (!insertSampleData($config)) {
    output("Installation failed.", true);
    exit(1);
}

output("Database installation completed successfully.", false);

if (!$isCli) {
    echo '<div class="success">You can now <a href="../index.php">login to the system</a>.</div>';
    echo '</body></html>';
}

exit(0);
