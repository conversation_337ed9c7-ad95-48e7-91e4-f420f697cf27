<?php
/**
 * Comprehensive CRUD Operations Debug Script
 * Tests all models and identifies issues preventing CRUD operations
 */

// Enable error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Start session
session_start();

echo "<h1>Comprehensive CRUD Operations Debug Test</h1>";
echo "<style>
    body { font-family: Arial, sans-serif; margin: 20px; }
    .success { color: green; }
    .error { color: red; }
    .warning { color: orange; }
    .info { color: blue; }
    .section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
    .test-result { margin: 5px 0; padding: 5px; }
    pre { background: #f5f5f5; padding: 10px; border-radius: 3px; overflow-x: auto; }
</style>";

// Test 1: Include all required files
echo "<div class='section'>";
echo "<h2>1. Testing File Includes</h2>";
$includeErrors = [];

try {
    require_once 'config/database.php';
    echo "<div class='test-result success'>✓ Database config loaded</div>";
} catch (Exception $e) {
    echo "<div class='test-result error'>✗ Database config error: " . $e->getMessage() . "</div>";
    $includeErrors[] = "Database config";
}

try {
    require_once 'includes/functions.php';
    echo "<div class='test-result success'>✓ Functions loaded</div>";
} catch (Exception $e) {
    echo "<div class='test-result error'>✗ Functions error: " . $e->getMessage() . "</div>";
    $includeErrors[] = "Functions";
}

try {
    require_once 'includes/auth.php';
    echo "<div class='test-result success'>✓ Auth functions loaded</div>";
} catch (Exception $e) {
    echo "<div class='test-result error'>✗ Auth error: " . $e->getMessage() . "</div>";
    $includeErrors[] = "Auth";
}

try {
    require_once 'includes/security.php';
    echo "<div class='test-result success'>✓ Security functions loaded</div>";
} catch (Exception $e) {
    echo "<div class='test-result error'>✗ Security error: " . $e->getMessage() . "</div>";
    $includeErrors[] = "Security";
}

try {
    require_once 'includes/notifications.php';
    echo "<div class='test-result success'>✓ Notifications functions loaded</div>";
} catch (Exception $e) {
    echo "<div class='test-result error'>✗ Notifications error: " . $e->getMessage() . "</div>";
    $includeErrors[] = "Notifications";
}

try {
    require_once 'models/Device.php';
    require_once 'models/Hospital.php';
    require_once 'models/Department.php';
    require_once 'models/User.php';
    require_once 'models/Ticket.php';
    require_once 'models/Maintenance.php';
    echo "<div class='test-result success'>✓ All models loaded</div>";
} catch (Exception $e) {
    echo "<div class='test-result error'>✗ Models error: " . $e->getMessage() . "</div>";
    $includeErrors[] = "Models";
}

if (!empty($includeErrors)) {
    echo "<div class='test-result error'><strong>Critical Error:</strong> Cannot proceed with missing includes: " . implode(', ', $includeErrors) . "</div>";
    echo "</div>";
    exit;
}
echo "</div>";

// Test 2: Database Connection
echo "<div class='section'>";
echo "<h2>2. Testing Database Connection</h2>";
if (isset($pdo)) {
    echo "<div class='test-result success'>✓ PDO connection established</div>";

    try {
        $stmt = $pdo->query("SELECT 1");
        echo "<div class='test-result success'>✓ Database query successful</div>";

        // Test database write permissions
        try {
            $pdo->exec("CREATE TEMPORARY TABLE test_write_permissions (id INT AUTO_INCREMENT PRIMARY KEY, test_data VARCHAR(50))");
            $pdo->exec("INSERT INTO test_write_permissions (test_data) VALUES ('test')");
            $stmt = $pdo->query("SELECT COUNT(*) FROM test_write_permissions");
            $count = $stmt->fetchColumn();
            echo "<div class='test-result success'>✓ Database write permissions: OK (inserted $count record)</div>";
            $pdo->exec("DROP TEMPORARY TABLE test_write_permissions");
        } catch (Exception $e) {
            echo "<div class='test-result error'>✗ Database write permissions failed: " . $e->getMessage() . "</div>";
        }

    } catch (Exception $e) {
        echo "<div class='test-result error'>✗ Database query failed: " . $e->getMessage() . "</div>";
        echo "</div>";
        exit;
    }
} else {
    echo "<div class='test-result error'>✗ No PDO connection - cannot proceed</div>";
    echo "</div>";
    exit;
}
echo "</div>";

// Test 3: Check Tables and Data
echo "<div class='section'>";
echo "<h2>3. Testing Database Tables and Data</h2>";
try {
    $stmt = $pdo->query("SHOW TABLES");
    $tables = $stmt->fetchAll(PDO::FETCH_COLUMN);

    $requiredTables = ['users', 'hospitals', 'departments', 'devices', 'maintenance_schedules', 'tickets'];
    $missingTables = [];
    $emptyTables = [];

    foreach ($requiredTables as $table) {
        if (in_array($table, $tables)) {
            $stmt = $pdo->query("SELECT COUNT(*) as count FROM $table");
            $count = $stmt->fetch()['count'];
            echo "<div class='test-result success'>✓ Table $table exists with $count records</div>";

            if ($count == 0 && in_array($table, ['users', 'hospitals'])) {
                $emptyTables[] = $table;
            }
        } else {
            echo "<div class='test-result error'>✗ Table $table missing</div>";
            $missingTables[] = $table;
        }
    }

    if (!empty($missingTables)) {
        echo "<div class='test-result error'><strong>Critical:</strong> Missing tables: " . implode(', ', $missingTables) . "</div>";
        echo "<div class='test-result info'>Run database/schema.sql to create missing tables</div>";
    }

    if (!empty($emptyTables)) {
        echo "<div class='test-result warning'><strong>Warning:</strong> Essential tables are empty: " . implode(', ', $emptyTables) . "</div>";
        echo "<div class='test-result info'>You need at least one admin user and one hospital to test CRUD operations</div>";
    }

} catch (Exception $e) {
    echo "<div class='test-result error'>✗ Table check error: " . $e->getMessage() . "</div>";
}
echo "</div>";

// Test 4: Model Instantiation and Method Testing
echo "<div class='section'>";
echo "<h2>4. Testing Model Instantiation and Methods</h2>";
$models = [];
try {
    $models['device'] = new Device($pdo);
    $models['hospital'] = new Hospital($pdo);
    $models['department'] = new Department($pdo);
    $models['user'] = new User($pdo);
    $models['ticket'] = new Ticket($pdo);
    $models['maintenance'] = new Maintenance($pdo);
    echo "<div class='test-result success'>✓ All models instantiated successfully</div>";

    // Test essential methods exist
    $requiredMethods = ['getAll', 'getById', 'create', 'update', 'delete'];
    foreach ($models as $modelName => $model) {
        $missingMethods = [];
        foreach ($requiredMethods as $method) {
            if (!method_exists($model, $method)) {
                $missingMethods[] = $method;
            }
        }

        if (empty($missingMethods)) {
            echo "<div class='test-result success'>✓ $modelName model has all required methods</div>";
        } else {
            echo "<div class='test-result error'>✗ $modelName model missing methods: " . implode(', ', $missingMethods) . "</div>";
        }
    }

} catch (Exception $e) {
    echo "<div class='test-result error'>✗ Model instantiation error: " . $e->getMessage() . "</div>";
    echo "</div>";
    exit;
}
echo "</div>";

// Test 5: Session and Authentication Testing
echo "<div class='section'>";
echo "<h2>5. Testing Session and Authentication</h2>";

// Test session functionality
echo "<div class='test-result info'>Session ID: " . session_id() . "</div>";
echo "<div class='test-result info'>Session status: " . (session_status() === PHP_SESSION_ACTIVE ? "Active" : "Inactive") . "</div>";

// Test authentication functions
if (function_exists('hasPermission')) {
    echo "<div class='test-result success'>✓ hasPermission function available</div>";

    // Test with mock session data
    $_SESSION['user'] = [
        'id' => 1,
        'username' => 'test_admin',
        'role' => 'admin',
        'permissions' => ['manage_devices', 'manage_hospitals', 'manage_departments']
    ];

    $testPermissions = ['manage_devices', 'manage_hospitals', 'view_reports'];
    foreach ($testPermissions as $permission) {
        $hasPermission = hasPermission($permission);
        echo "<div class='test-result " . ($hasPermission ? "success" : "warning") . "'>" .
             ($hasPermission ? "✓" : "⚠") . " Permission '$permission': " .
             ($hasPermission ? "Granted" : "Denied") . "</div>";
    }
} else {
    echo "<div class='test-result error'>✗ hasPermission function not available</div>";
}

// Test CSRF token generation
if (function_exists('generateCSRFToken')) {
    try {
        $token = generateCSRFToken();
        echo "<div class='test-result success'>✓ CSRF token generated: " . substr($token, 0, 20) . "...</div>";

        if (function_exists('validateCSRFToken')) {
            $valid = validateCSRFToken($token);
            echo "<div class='test-result " . ($valid ? "success" : "error") . "'>" .
                 ($valid ? "✓" : "✗") . " CSRF token validation: " .
                 ($valid ? "Valid" : "Invalid") . "</div>";
        }
    } catch (Exception $e) {
        echo "<div class='test-result error'>✗ CSRF token error: " . $e->getMessage() . "</div>";
    }
} else {
    echo "<div class='test-result error'>✗ generateCSRFToken function not available</div>";
}
echo "</div>";

// Test 6: Create Test Data (if needed)
echo "<div class='section'>";
echo "<h2>6. Creating Test Data (if needed)</h2>";

// Check if we have hospitals
$hospitals = $models['hospital']->getAll();
if (empty($hospitals)) {
    echo "<div class='test-result info'>Creating test hospital...</div>";
    $hospitalId = $models['hospital']->create([
        'name' => 'Test Hospital',
        'address' => '123 Test Street',
        'city' => 'Test City',
        'country' => 'Test Country',
        'phone' => '************',
        'email' => '<EMAIL>'
    ]);

    if ($hospitalId) {
        echo "<div class='test-result success'>✓ Test hospital created (ID: $hospitalId)</div>";
    } else {
        echo "<div class='test-result error'>✗ Failed to create test hospital</div>";
    }
} else {
    echo "<div class='test-result success'>✓ Hospitals exist (" . count($hospitals) . " found)</div>";
    $hospitalId = $hospitals[0]['id'];
}

// Check if we have departments
$departments = $models['department']->getAll();
if (empty($departments)) {
    echo "<div class='test-result info'>Creating test department...</div>";
    $departmentId = $models['department']->create([
        'hospital_id' => $hospitalId,
        'name' => 'Test Department',
        'location' => 'Floor 1',
        'phone' => '************',
        'email' => '<EMAIL>'
    ]);

    if ($departmentId) {
        echo "<div class='test-result success'>✓ Test department created (ID: $departmentId)</div>";
    } else {
        echo "<div class='test-result error'>✗ Failed to create test department</div>";
    }
} else {
    echo "<div class='test-result success'>✓ Departments exist (" . count($departments) . " found)</div>";
    $departmentId = $departments[0]['id'];
}
echo "</div>";

// Test 7: Comprehensive CRUD Operations Testing
echo "<div class='section'>";
echo "<h2>7. Testing CRUD Operations on All Models</h2>";

$crudResults = [];

// Test each model's CRUD operations
$modelsToTest = [
    'hospital' => [
        'data' => [
            'name' => 'CRUD Test Hospital ' . time(),
            'address' => '123 CRUD Test Street',
            'city' => 'Test City',
            'country' => 'Test Country',
            'phone' => '************',
            'email' => '<EMAIL>'
        ],
        'update_field' => 'name',
        'update_value' => 'Updated CRUD Test Hospital ' . time()
    ],
    'device' => [
        'data' => [
            'hospital_id' => $hospitalId,
            'department_id' => $departmentId,
            'name' => 'CRUD Test Device ' . time(),
            'model' => 'Test Model',
            'serial_number' => 'CRUD-TEST-' . time(),
            'manufacturer' => 'Test Manufacturer',
            'category' => 'Other',
            'purchase_date' => date('Y-m-d'),
            'warranty_expiry' => date('Y-m-d', strtotime('+1 year')),
            'status' => 'operational',
            'location' => 'Test Location',
            'maintenance_interval' => 30,
            'notes' => 'CRUD test device'
        ],
        'update_field' => 'name',
        'update_value' => 'Updated CRUD Test Device ' . time()
    ],
    'maintenance' => [
        'data' => [
            'type' => 'schedule',
            'device_id' => null, // Will be set to a valid device ID
            'title' => 'CRUD Test Maintenance ' . time(),
            'description' => 'Test maintenance schedule for CRUD testing',
            'scheduled_date' => date('Y-m-d', strtotime('+7 days')),
            'frequency' => 'monthly',
            'status' => 'scheduled',
            'created_by' => 1 // Assuming admin user ID is 1
        ],
        'update_field' => 'title',
        'update_value' => 'Updated CRUD Test Maintenance ' . time()
    ],
    'ticket' => [
        'data' => [
            'device_id' => null, // Will be set to a valid device ID
            'reported_by' => 1, // Assuming admin user ID is 1
            'assigned_to' => 1,
            'title' => 'CRUD Test Ticket ' . time(),
            'description' => 'Test ticket for CRUD testing',
            'priority' => 'medium',
            'status' => 'open'
        ],
        'update_field' => 'title',
        'update_value' => 'Updated CRUD Test Ticket ' . time()
    ]
];

foreach ($modelsToTest as $modelName => $testConfig) {
    echo "<h3>Testing $modelName Model CRUD Operations</h3>";
    $model = $models[$modelName];
    $testData = $testConfig['data'];

    // Special handling for models that need a valid device ID
    if ($modelName === 'maintenance' || $modelName === 'ticket') {
        // Get a device ID from the device model test or create one
        if (isset($crudResults['device']['id'])) {
            $testData['device_id'] = $crudResults['device']['id'];
        } else {
            // Try to get any existing device
            $devices = $models['device']->getAll();
            if (!empty($devices)) {
                $testData['device_id'] = $devices[0]['id'];
            } else {
                echo "<div class='test-result warning'>⚠ Skipping $modelName test - no devices available</div>";
                continue;
            }
        }
    }

    // CREATE Test
    echo "<div class='test-result info'><strong>CREATE Test:</strong></div>";
    try {
        $recordId = $model->create($testData);
        if ($recordId) {
            echo "<div class='test-result success'>✓ $modelName created successfully (ID: $recordId)</div>";
            $crudResults[$modelName]['create'] = true;
            $crudResults[$modelName]['id'] = $recordId;
        } else {
            echo "<div class='test-result error'>✗ Failed to create $modelName</div>";
            $crudResults[$modelName]['create'] = false;
            continue; // Skip other tests if create fails
        }
    } catch (Exception $e) {
        echo "<div class='test-result error'>✗ $modelName create error: " . $e->getMessage() . "</div>";
        $crudResults[$modelName]['create'] = false;
        continue;
    }

    // READ Test
    echo "<div class='test-result info'><strong>READ Test:</strong></div>";
    try {
        // Special handling for maintenance model
        if ($modelName === 'maintenance') {
            $record = $model->getById($recordId, 'schedule');
        } else {
            $record = $model->getById($recordId);
        }

        if ($record) {
            echo "<div class='test-result success'>✓ $modelName retrieved successfully</div>";
            echo "<div class='test-result info'>  - ID: " . $record['id'] . "</div>";

            // Display appropriate name field based on model
            if ($modelName === 'maintenance') {
                echo "<div class='test-result info'>  - Title: " . ($record['title'] ?? 'N/A') . "</div>";
            } elseif ($modelName === 'ticket') {
                echo "<div class='test-result info'>  - Title: " . ($record['title'] ?? 'N/A') . "</div>";
            } else {
                echo "<div class='test-result info'>  - Name: " . ($record['name'] ?? 'N/A') . "</div>";
            }

            $crudResults[$modelName]['read'] = true;
        } else {
            echo "<div class='test-result error'>✗ Failed to retrieve $modelName</div>";
            $crudResults[$modelName]['read'] = false;
        }
    } catch (Exception $e) {
        echo "<div class='test-result error'>✗ $modelName read error: " . $e->getMessage() . "</div>";
        $crudResults[$modelName]['read'] = false;
    }

    // UPDATE Test
    echo "<div class='test-result info'><strong>UPDATE Test:</strong></div>";
    try {
        $updateData = $testData;
        $updateData[$testConfig['update_field']] = $testConfig['update_value'];

        $updateResult = $model->update($recordId, $updateData);
        if ($updateResult) {
            echo "<div class='test-result success'>✓ $modelName updated successfully</div>";

            // Verify update
            if ($modelName === 'maintenance') {
                $updatedRecord = $model->getById($recordId, 'schedule');
            } else {
                $updatedRecord = $model->getById($recordId);
            }

            if ($updatedRecord && $updatedRecord[$testConfig['update_field']] === $testConfig['update_value']) {
                echo "<div class='test-result success'>✓ Update verified</div>";
                $crudResults[$modelName]['update'] = true;
            } else {
                echo "<div class='test-result error'>✗ Update verification failed</div>";
                $crudResults[$modelName]['update'] = false;
            }
        } else {
            echo "<div class='test-result error'>✗ Failed to update $modelName</div>";
            $crudResults[$modelName]['update'] = false;
        }
    } catch (Exception $e) {
        echo "<div class='test-result error'>✗ $modelName update error: " . $e->getMessage() . "</div>";
        $crudResults[$modelName]['update'] = false;
    }

    // DELETE Test
    echo "<div class='test-result info'><strong>DELETE Test:</strong></div>";
    try {
        // Special handling for maintenance model
        if ($modelName === 'maintenance') {
            $deleteResult = $model->delete($recordId, 'schedule');
        } else {
            $deleteResult = $model->delete($recordId);
        }

        if ($deleteResult) {
            echo "<div class='test-result success'>✓ $modelName deleted successfully</div>";

            // Verify deletion
            if ($modelName === 'maintenance') {
                $deletedRecord = $model->getById($recordId, 'schedule');
            } else {
                $deletedRecord = $model->getById($recordId);
            }

            if (!$deletedRecord) {
                echo "<div class='test-result success'>✓ Deletion verified</div>";
                $crudResults[$modelName]['delete'] = true;
            } else {
                echo "<div class='test-result error'>✗ Deletion verification failed</div>";
                $crudResults[$modelName]['delete'] = false;
            }
        } else {
            echo "<div class='test-result error'>✗ Failed to delete $modelName</div>";
            $crudResults[$modelName]['delete'] = false;
        }
    } catch (Exception $e) {
        echo "<div class='test-result error'>✗ $modelName delete error: " . $e->getMessage() . "</div>";
        $crudResults[$modelName]['delete'] = false;
    }

    echo "<hr>";
}
echo "</div>";

// Test 8: Summary and Recommendations
echo "<div class='section'>";
echo "<h2>8. Test Summary and Recommendations</h2>";

// Display CRUD results summary
echo "<h3>CRUD Operations Summary</h3>";
$allPassed = true;
foreach ($crudResults as $modelName => $results) {
    echo "<h4>$modelName Model:</h4>";
    $operations = ['create', 'read', 'update', 'delete'];
    foreach ($operations as $operation) {
        $status = isset($results[$operation]) && $results[$operation];
        $class = $status ? 'success' : 'error';
        $icon = $status ? '✓' : '✗';
        echo "<div class='test-result $class'>$icon " . ucfirst($operation) . ": " . ($status ? "PASSED" : "FAILED") . "</div>";
        if (!$status) $allPassed = false;
    }
}

echo "<h3>Overall Assessment</h3>";
if ($allPassed) {
    echo "<div class='test-result success'><strong>✓ ALL CRUD OPERATIONS WORKING CORRECTLY</strong></div>";
    echo "<div class='test-result info'>Your system should be able to add, edit, and delete data properly.</div>";
} else {
    echo "<div class='test-result error'><strong>✗ SOME CRUD OPERATIONS FAILED</strong></div>";
    echo "<div class='test-result warning'>Check the specific error messages above to identify issues.</div>";
}

echo "<h3>Common Issues and Solutions</h3>";
echo "<div class='test-result info'><strong>If CRUD operations are failing:</strong></div>";
echo "<div class='test-result info'>1. <strong>Database Connection:</strong> Ensure MySQL is running and credentials are correct</div>";
echo "<div class='test-result info'>2. <strong>Permissions:</strong> Check user permissions in the application</div>";
echo "<div class='test-result info'>3. <strong>Session Issues:</strong> Clear browser cache and cookies</div>";
echo "<div class='test-result info'>4. <strong>CSRF Tokens:</strong> Ensure forms include valid CSRF tokens</div>";
echo "<div class='test-result info'>5. <strong>PHP Errors:</strong> Check PHP error logs for detailed error messages</div>";
echo "<div class='test-result info'>6. <strong>Database Schema:</strong> Ensure all required tables exist with correct structure</div>";

echo "<h3>Next Steps</h3>";
if ($allPassed) {
    echo "<div class='test-result success'>✓ Try using the main application - CRUD operations should work</div>";
    echo "<div class='test-result info'>→ <a href='index.php'>Open Main Application</a></div>";
} else {
    echo "<div class='test-result warning'>→ Fix the issues identified above before using the main application</div>";
    echo "<div class='test-result info'>→ Re-run this test after making fixes</div>";
}

echo "</div>";

echo "<div class='section'>";
echo "<h2>Debug Test Complete</h2>";
echo "<p><strong>Test completed at:</strong> " . date('Y-m-d H:i:s') . "</p>";
echo "<p><strong>PHP Version:</strong> " . phpversion() . "</p>";
echo "<p><strong>Database:</strong> " . (isset($pdo) ? "Connected" : "Not Connected") . "</p>";
echo "</div>";
?>
