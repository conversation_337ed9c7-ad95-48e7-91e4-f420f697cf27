<?php
/**
 * Maintenance Model Test Script
 * Tests the newly added standard CRUD methods
 */

// Enable error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>Maintenance Model CRUD Methods Test</h1>";
echo "<style>
    body { font-family: Arial, sans-serif; margin: 20px; }
    .success { color: green; }
    .error { color: red; }
    .info { color: blue; }
    .section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
</style>";

try {
    // Include required files
    require_once 'config/database.php';
    require_once 'models/Maintenance.php';
    require_once 'models/Device.php';
    require_once 'models/Hospital.php';
    require_once 'models/Department.php';
    
    echo "<div class='section'>";
    echo "<h2>1. Model Instantiation Test</h2>";
    
    $maintenanceModel = new Maintenance($pdo);
    echo "<div class='success'>✓ Maintenance model instantiated</div>";
    
    // Test method existence
    $requiredMethods = ['getAll', 'getById', 'create', 'update', 'delete'];
    foreach ($requiredMethods as $method) {
        if (method_exists($maintenanceModel, $method)) {
            echo "<div class='success'>✓ Method '$method' exists</div>";
        } else {
            echo "<div class='error'>✗ Method '$method' missing</div>";
        }
    }
    echo "</div>";
    
    echo "<div class='section'>";
    echo "<h2>2. Database Connection Test</h2>";
    
    // Test database connection
    $stmt = $pdo->query("SELECT 1");
    echo "<div class='success'>✓ Database connection working</div>";
    
    // Check if required tables exist
    $requiredTables = ['maintenance_schedules', 'maintenance_logs', 'devices', 'hospitals', 'departments'];
    foreach ($requiredTables as $table) {
        try {
            $stmt = $pdo->query("SELECT COUNT(*) FROM $table");
            $count = $stmt->fetchColumn();
            echo "<div class='success'>✓ Table '$table' exists with $count records</div>";
        } catch (Exception $e) {
            echo "<div class='error'>✗ Table '$table' error: " . $e->getMessage() . "</div>";
        }
    }
    echo "</div>";
    
    echo "<div class='section'>";
    echo "<h2>3. Test Data Preparation</h2>";
    
    // Get or create test data
    $deviceModel = new Device($pdo);
    $devices = $deviceModel->getAll();
    
    if (empty($devices)) {
        echo "<div class='error'>✗ No devices found - cannot test maintenance without devices</div>";
        echo "<div class='info'>Please create at least one device first</div>";
        exit;
    }
    
    $testDeviceId = $devices[0]['id'];
    echo "<div class='success'>✓ Using device ID $testDeviceId for testing</div>";
    echo "</div>";
    
    echo "<div class='section'>";
    echo "<h2>4. CRUD Operations Test</h2>";
    
    // Test CREATE
    echo "<h3>CREATE Test</h3>";
    $testScheduleData = [
        'type' => 'schedule',
        'device_id' => $testDeviceId,
        'title' => 'Test Maintenance Schedule ' . time(),
        'description' => 'This is a test maintenance schedule created by the CRUD test',
        'scheduled_date' => date('Y-m-d', strtotime('+7 days')),
        'frequency' => 'monthly',
        'status' => 'scheduled',
        'created_by' => 1 // Assuming admin user ID is 1
    ];
    
    $scheduleId = $maintenanceModel->create($testScheduleData);
    if ($scheduleId) {
        echo "<div class='success'>✓ Maintenance schedule created successfully (ID: $scheduleId)</div>";
    } else {
        echo "<div class='error'>✗ Failed to create maintenance schedule</div>";
        exit;
    }
    
    // Test READ
    echo "<h3>READ Test</h3>";
    $schedule = $maintenanceModel->getById($scheduleId, 'schedule');
    if ($schedule) {
        echo "<div class='success'>✓ Maintenance schedule retrieved successfully</div>";
        echo "<div class='info'>  - ID: " . $schedule['id'] . "</div>";
        echo "<div class='info'>  - Title: " . $schedule['title'] . "</div>";
        echo "<div class='info'>  - Status: " . $schedule['status'] . "</div>";
    } else {
        echo "<div class='error'>✗ Failed to retrieve maintenance schedule</div>";
    }
    
    // Test getAll method
    echo "<h3>GET ALL Test</h3>";
    $allRecords = $maintenanceModel->getAll();
    echo "<div class='success'>✓ Retrieved " . count($allRecords) . " maintenance records</div>";
    
    // Test UPDATE
    echo "<h3>UPDATE Test</h3>";
    $updateData = $testScheduleData;
    $updateData['title'] = 'Updated Test Maintenance Schedule ' . time();
    $updateData['description'] = 'This schedule has been updated by the CRUD test';
    
    $updateResult = $maintenanceModel->update($scheduleId, $updateData);
    if ($updateResult) {
        echo "<div class='success'>✓ Maintenance schedule updated successfully</div>";
        
        // Verify update
        $updatedSchedule = $maintenanceModel->getById($scheduleId, 'schedule');
        if ($updatedSchedule && $updatedSchedule['title'] === $updateData['title']) {
            echo "<div class='success'>✓ Update verified - title changed correctly</div>";
        } else {
            echo "<div class='error'>✗ Update verification failed</div>";
        }
    } else {
        echo "<div class='error'>✗ Failed to update maintenance schedule</div>";
    }
    
    // Test DELETE
    echo "<h3>DELETE Test</h3>";
    $deleteResult = $maintenanceModel->delete($scheduleId, 'schedule');
    if ($deleteResult) {
        echo "<div class='success'>✓ Maintenance schedule deleted successfully</div>";
        
        // Verify deletion
        $deletedSchedule = $maintenanceModel->getById($scheduleId, 'schedule');
        if (!$deletedSchedule) {
            echo "<div class='success'>✓ Deletion verified - record no longer exists</div>";
        } else {
            echo "<div class='error'>✗ Deletion verification failed - record still exists</div>";
        }
    } else {
        echo "<div class='error'>✗ Failed to delete maintenance schedule</div>";
    }
    
    echo "</div>";
    
    echo "<div class='section'>";
    echo "<h2>5. Test Summary</h2>";
    echo "<div class='success'><strong>✓ All Maintenance Model CRUD Methods Working Correctly</strong></div>";
    echo "<div class='info'>The maintenance model now has all required standard CRUD methods:</div>";
    echo "<div class='info'>- getAll() - Retrieves all maintenance records (schedules and logs)</div>";
    echo "<div class='info'>- getById() - Retrieves a specific record by ID</div>";
    echo "<div class='info'>- create() - Creates new maintenance schedules or logs</div>";
    echo "<div class='info'>- update() - Updates existing records</div>";
    echo "<div class='info'>- delete() - Deletes records</div>";
    echo "<div class='info'>These methods work alongside the existing specialized methods.</div>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div class='error'><strong>Test Error:</strong> " . $e->getMessage() . "</div>";
    echo "<div class='info'>Stack trace:</div>";
    echo "<pre>" . $e->getTraceAsString() . "</pre>";
}

echo "<div class='section'>";
echo "<h2>Next Steps</h2>";
echo "<div class='info'>1. Run the main diagnostic test: <a href='debug_crud.php'>debug_crud.php</a></div>";
echo "<div class='info'>2. Test the full application: <a href='index.php'>index.php</a></div>";
echo "<div class='info'>3. Check other models if needed</div>";
echo "</div>";
?>
