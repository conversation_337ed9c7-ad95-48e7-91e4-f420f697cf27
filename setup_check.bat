@echo off
echo ===================================================
echo Medical Device Management System - Setup Check
echo ===================================================
echo.

echo 1. Checking for PHP installation...
php --version >nul 2>&1
if %errorlevel% == 0 (
    echo [OK] PHP is installed and accessible
    php --version
) else (
    echo [ERROR] PHP is not installed or not in PATH
    echo.
    echo Solutions:
    echo - Install XAMPP from https://www.apachefriends.org/
    echo - Or install WAMP from http://www.wampserver.com/
    echo - Or add PHP to your system PATH
    echo.
)

echo.
echo 2. Checking for MySQL/MariaDB...
mysql --version >nul 2>&1
if %errorlevel% == 0 (
    echo [OK] MySQL/MariaDB is accessible
    mysql --version
) else (
    echo [ERROR] MySQL/MariaDB is not installed or not in PATH
    echo Install XAMPP or WAMP to get MySQL
)

echo.
echo 3. Checking for Apache/Web Server...
netstat -an | findstr :80 >nul 2>&1
if %errorlevel% == 0 (
    echo [OK] Something is running on port 80
    netstat -an | findstr :80
) else (
    echo [ERROR] No web server running on port 80
    echo You need to start Apache or another web server
)

echo.
echo 4. Checking project files...
if exist "index.php" (
    echo [OK] index.php found
) else (
    echo [ERROR] index.php not found - are you in the right directory?
)

if exist "config\database.php" (
    echo [OK] database.php found
) else (
    echo [ERROR] config\database.php not found
)

if exist "models\Device.php" (
    echo [OK] Device.php model found
) else (
    echo [ERROR] models\Device.php not found
)

echo.
echo 5. Checking directories...
if exist "uploads" (
    echo [OK] uploads directory exists
) else (
    echo [ERROR] uploads directory missing
    mkdir uploads
    echo [FIXED] Created uploads directory
)

if exist "uploads\qrcodes" (
    echo [OK] uploads\qrcodes directory exists
) else (
    echo [ERROR] uploads\qrcodes directory missing
    mkdir uploads\qrcodes
    echo [FIXED] Created uploads\qrcodes directory
)

echo.
echo ===================================================
echo Setup Check Complete
echo ===================================================
echo.
echo If you see PHP or MySQL errors above, you need to:
echo 1. Install XAMPP or WAMP
echo 2. Start Apache and MySQL services
echo 3. Move this project to the web server directory
echo 4. Access via http://localhost/bio
echo.
pause
