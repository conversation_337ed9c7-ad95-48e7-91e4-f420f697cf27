<?php
/**
 * Simple CRUD Test for Maintenance and Ticket Models
 * Tests basic operations without complex dependencies
 */

// Enable error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>Simple CRUD Test</h1>";
echo "<style>
    body { font-family: Arial, sans-serif; margin: 20px; }
    .success { color: green; }
    .error { color: red; }
    .info { color: blue; }
    .section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
</style>";

try {
    // Include only essential files
    require_once 'config/database.php';
    require_once 'models/Maintenance.php';
    require_once 'models/Ticket.php';
    require_once 'models/Device.php';
    require_once 'models/Hospital.php';
    require_once 'models/Department.php';
    
    echo "<div class='success'>✓ Essential files loaded</div>";
    
    // Create models
    $maintenanceModel = new Maintenance($pdo);
    $ticketModel = new Ticket($pdo);
    $deviceModel = new Device($pdo);
    
    echo "<div class='success'>✓ Models instantiated</div>";
    
} catch (Exception $e) {
    echo "<div class='error'>✗ Setup error: " . $e->getMessage() . "</div>";
    exit;
}

echo "<div class='section'>";
echo "<h2>Testing Maintenance Model</h2>";

// Get a device for testing
$devices = $deviceModel->getAll();
if (empty($devices)) {
    echo "<div class='error'>✗ No devices available for testing</div>";
} else {
    $deviceId = $devices[0]['id'];
    echo "<div class='info'>Using device ID: $deviceId</div>";
    
    // Test maintenance schedule creation using the specialized method
    echo "<h3>Testing createSchedule method</h3>";
    try {
        $scheduleData = [
            'device_id' => $deviceId,
            'title' => 'Simple Test Schedule',
            'description' => 'Test schedule',
            'scheduled_date' => date('Y-m-d', strtotime('+7 days')),
            'frequency' => 'monthly',
            'status' => 'scheduled',
            'created_by' => 1
        ];
        
        $scheduleId = $maintenanceModel->createSchedule($scheduleData);
        if ($scheduleId) {
            echo "<div class='success'>✓ Schedule created with ID: $scheduleId</div>";
            
            // Test reading the schedule
            $schedule = $maintenanceModel->getScheduleById($scheduleId);
            if ($schedule) {
                echo "<div class='success'>✓ Schedule retrieved successfully</div>";
                
                // Test updating the schedule
                $scheduleData['title'] = 'Updated Simple Test Schedule';
                $updateResult = $maintenanceModel->updateSchedule($scheduleId, $scheduleData);
                if ($updateResult) {
                    echo "<div class='success'>✓ Schedule updated successfully</div>";
                } else {
                    echo "<div class='error'>✗ Schedule update failed</div>";
                }
                
                // Test deleting the schedule
                $deleteResult = $maintenanceModel->deleteSchedule($scheduleId);
                if ($deleteResult) {
                    echo "<div class='success'>✓ Schedule deleted successfully</div>";
                } else {
                    echo "<div class='error'>✗ Schedule delete failed</div>";
                }
            } else {
                echo "<div class='error'>✗ Failed to retrieve schedule</div>";
            }
        } else {
            echo "<div class='error'>✗ Failed to create schedule</div>";
        }
    } catch (Exception $e) {
        echo "<div class='error'>✗ Maintenance test error: " . $e->getMessage() . "</div>";
    }
    
    // Test the new standard CRUD methods
    echo "<h3>Testing Standard CRUD Methods</h3>";
    try {
        $crudData = [
            'type' => 'schedule',
            'device_id' => $deviceId,
            'title' => 'CRUD Test Schedule',
            'description' => 'Test schedule via CRUD',
            'scheduled_date' => date('Y-m-d', strtotime('+7 days')),
            'frequency' => 'monthly',
            'status' => 'scheduled',
            'created_by' => 1
        ];
        
        $crudId = $maintenanceModel->create($crudData);
        if ($crudId) {
            echo "<div class='success'>✓ CRUD create successful with ID: $crudId</div>";
            
            $record = $maintenanceModel->getById($crudId, 'schedule');
            if ($record) {
                echo "<div class='success'>✓ CRUD read successful</div>";
                
                $crudData['title'] = 'Updated CRUD Test Schedule';
                $updateResult = $maintenanceModel->update($crudId, $crudData);
                if ($updateResult) {
                    echo "<div class='success'>✓ CRUD update successful</div>";
                } else {
                    echo "<div class='error'>✗ CRUD update failed</div>";
                }
                
                $deleteResult = $maintenanceModel->delete($crudId, 'schedule');
                if ($deleteResult) {
                    echo "<div class='success'>✓ CRUD delete successful</div>";
                } else {
                    echo "<div class='error'>✗ CRUD delete failed</div>";
                }
            } else {
                echo "<div class='error'>✗ CRUD read failed</div>";
            }
        } else {
            echo "<div class='error'>✗ CRUD create failed</div>";
        }
    } catch (Exception $e) {
        echo "<div class='error'>✗ CRUD test error: " . $e->getMessage() . "</div>";
    }
}
echo "</div>";

echo "<div class='section'>";
echo "<h2>Testing Ticket Model</h2>";

if (!empty($devices)) {
    $deviceId = $devices[0]['id'];
    echo "<div class='info'>Using device ID: $deviceId</div>";
    
    // Test ticket creation
    echo "<h3>Testing Ticket CRUD</h3>";
    try {
        $ticketData = [
            'device_id' => $deviceId,
            'reported_by' => 1,
            'assigned_to' => 1,
            'title' => 'Simple Test Ticket',
            'description' => 'Test ticket description',
            'priority' => 'medium',
            'status' => 'open'
        ];
        
        $ticketId = $ticketModel->create($ticketData);
        if ($ticketId) {
            echo "<div class='success'>✓ Ticket created with ID: $ticketId</div>";
            
            // Test reading the ticket
            $ticket = $ticketModel->getById($ticketId);
            if ($ticket) {
                echo "<div class='success'>✓ Ticket retrieved successfully</div>";
                
                // Test updating the ticket
                $ticketData['title'] = 'Updated Simple Test Ticket';
                $updateResult = $ticketModel->update($ticketId, $ticketData);
                if ($updateResult) {
                    echo "<div class='success'>✓ Ticket updated successfully</div>";
                } else {
                    echo "<div class='error'>✗ Ticket update failed</div>";
                }
                
                // Test deleting the ticket
                $deleteResult = $ticketModel->delete($ticketId);
                if ($deleteResult) {
                    echo "<div class='success'>✓ Ticket deleted successfully</div>";
                } else {
                    echo "<div class='error'>✗ Ticket delete failed</div>";
                }
            } else {
                echo "<div class='error'>✗ Failed to retrieve ticket</div>";
            }
        } else {
            echo "<div class='error'>✗ Failed to create ticket</div>";
        }
    } catch (Exception $e) {
        echo "<div class='error'>✗ Ticket test error: " . $e->getMessage() . "</div>";
    }
}
echo "</div>";

echo "<div class='section'>";
echo "<h2>Test Summary</h2>";
echo "<div class='info'>This test checks if the basic CRUD operations work without complex dependencies.</div>";
echo "<div class='info'>If this test passes, the models should work in the main diagnostic script.</div>";
echo "<div class='info'>Next: Run <a href='debug_crud.php'>debug_crud.php</a> to test with full dependencies.</div>";
echo "</div>";
?>
