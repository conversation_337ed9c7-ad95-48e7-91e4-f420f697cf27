<?php
/**
 * Specific Debug Script for Maintenance and Ticket Models
 * This script provides detailed error information for troubleshooting
 */

// Enable error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Start session
session_start();

echo "<h1>Detailed Debug for Maintenance and Ticket Models</h1>";
echo "<style>
    body { font-family: Arial, sans-serif; margin: 20px; }
    .success { color: green; }
    .error { color: red; }
    .warning { color: orange; }
    .info { color: blue; }
    .section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
    pre { background: #f5f5f5; padding: 10px; border-radius: 3px; overflow-x: auto; }
</style>";

// Include all required files
try {
    require_once 'config/database.php';
    require_once 'includes/functions.php';
    require_once 'includes/auth.php';
    require_once 'includes/security.php';
    require_once 'includes/notifications.php';
    require_once 'models/Device.php';
    require_once 'models/Hospital.php';
    require_once 'models/Department.php';
    require_once 'models/User.php';
    require_once 'models/Ticket.php';
    require_once 'models/Maintenance.php';
    echo "<div class='success'>✓ All files included successfully</div>";
} catch (Exception $e) {
    echo "<div class='error'>✗ Include error: " . $e->getMessage() . "</div>";
    exit;
}

// Set up test session data
$_SESSION['user'] = [
    'id' => 1,
    'username' => 'test_admin',
    'role' => 'admin',
    'permissions' => ['manage_devices', 'manage_hospitals', 'manage_departments', 'manage_maintenance', 'manage_tickets']
];

echo "<div class='section'>";
echo "<h2>1. Testing Model Instantiation</h2>";

try {
    $maintenanceModel = new Maintenance($pdo);
    echo "<div class='success'>✓ Maintenance model instantiated</div>";
} catch (Exception $e) {
    echo "<div class='error'>✗ Maintenance model error: " . $e->getMessage() . "</div>";
    exit;
}

try {
    $ticketModel = new Ticket($pdo);
    echo "<div class='success'>✓ Ticket model instantiated</div>";
} catch (Exception $e) {
    echo "<div class='error'>✗ Ticket model error: " . $e->getMessage() . "</div>";
    exit;
}

try {
    $deviceModel = new Device($pdo);
    $hospitalModel = new Hospital($pdo);
    $departmentModel = new Department($pdo);
    echo "<div class='success'>✓ Supporting models instantiated</div>";
} catch (Exception $e) {
    echo "<div class='error'>✗ Supporting models error: " . $e->getMessage() . "</div>";
    exit;
}
echo "</div>";

echo "<div class='section'>";
echo "<h2>2. Preparing Test Data</h2>";

// Get or create test data
$devices = $deviceModel->getAll();
if (empty($devices)) {
    echo "<div class='error'>✗ No devices found - creating test device</div>";
    
    // Create test hospital and department first
    $hospitals = $hospitalModel->getAll();
    if (empty($hospitals)) {
        $hospitalId = $hospitalModel->create([
            'name' => 'Test Hospital',
            'address' => '123 Test Street',
            'city' => 'Test City',
            'country' => 'Test Country',
            'phone' => '************',
            'email' => '<EMAIL>'
        ]);
        echo "<div class='success'>✓ Test hospital created (ID: $hospitalId)</div>";
    } else {
        $hospitalId = $hospitals[0]['id'];
        echo "<div class='success'>✓ Using existing hospital (ID: $hospitalId)</div>";
    }
    
    $departments = $departmentModel->getByHospital($hospitalId);
    if (empty($departments)) {
        $departmentId = $departmentModel->create([
            'hospital_id' => $hospitalId,
            'name' => 'Test Department',
            'location' => 'Floor 1',
            'phone' => '************',
            'email' => '<EMAIL>'
        ]);
        echo "<div class='success'>✓ Test department created (ID: $departmentId)</div>";
    } else {
        $departmentId = $departments[0]['id'];
        echo "<div class='success'>✓ Using existing department (ID: $departmentId)</div>";
    }
    
    // Create test device
    $deviceId = $deviceModel->create([
        'hospital_id' => $hospitalId,
        'department_id' => $departmentId,
        'name' => 'Test Device for CRUD',
        'model' => 'Test Model',
        'serial_number' => 'TEST-CRUD-' . time(),
        'manufacturer' => 'Test Manufacturer',
        'category' => 'Other',
        'purchase_date' => date('Y-m-d'),
        'warranty_expiry' => date('Y-m-d', strtotime('+1 year')),
        'status' => 'operational',
        'location' => 'Test Location',
        'maintenance_interval' => 30,
        'notes' => 'Test device for CRUD testing'
    ]);
    
    if ($deviceId) {
        echo "<div class='success'>✓ Test device created (ID: $deviceId)</div>";
    } else {
        echo "<div class='error'>✗ Failed to create test device</div>";
        exit;
    }
} else {
    $deviceId = $devices[0]['id'];
    echo "<div class='success'>✓ Using existing device (ID: $deviceId)</div>";
}
echo "</div>";

echo "<div class='section'>";
echo "<h2>3. Testing Maintenance Model CRUD</h2>";

// Test Maintenance CREATE
echo "<h3>Maintenance CREATE Test</h3>";
$maintenanceData = [
    'type' => 'schedule',
    'device_id' => $deviceId,
    'title' => 'Detailed Test Maintenance ' . time(),
    'description' => 'This is a detailed test maintenance schedule',
    'scheduled_date' => date('Y-m-d', strtotime('+7 days')),
    'frequency' => 'monthly',
    'status' => 'scheduled',
    'created_by' => 1
];

try {
    echo "<div class='info'>Attempting to create maintenance with data:</div>";
    echo "<pre>" . print_r($maintenanceData, true) . "</pre>";
    
    $maintenanceId = $maintenanceModel->create($maintenanceData);
    if ($maintenanceId) {
        echo "<div class='success'>✓ Maintenance created successfully (ID: $maintenanceId)</div>";
    } else {
        echo "<div class='error'>✗ Maintenance create returned false</div>";
    }
} catch (Exception $e) {
    echo "<div class='error'>✗ Maintenance create exception: " . $e->getMessage() . "</div>";
    echo "<div class='info'>Stack trace:</div>";
    echo "<pre>" . $e->getTraceAsString() . "</pre>";
}

// Test Maintenance READ (if create succeeded)
if (isset($maintenanceId) && $maintenanceId) {
    echo "<h3>Maintenance READ Test</h3>";
    try {
        $maintenance = $maintenanceModel->getById($maintenanceId, 'schedule');
        if ($maintenance) {
            echo "<div class='success'>✓ Maintenance retrieved successfully</div>";
            echo "<div class='info'>Retrieved data:</div>";
            echo "<pre>" . print_r($maintenance, true) . "</pre>";
        } else {
            echo "<div class='error'>✗ Maintenance read returned false</div>";
        }
    } catch (Exception $e) {
        echo "<div class='error'>✗ Maintenance read exception: " . $e->getMessage() . "</div>";
    }
    
    // Test Maintenance UPDATE
    echo "<h3>Maintenance UPDATE Test</h3>";
    try {
        $updateData = $maintenanceData;
        $updateData['title'] = 'Updated Detailed Test Maintenance ' . time();
        
        $updateResult = $maintenanceModel->update($maintenanceId, $updateData);
        if ($updateResult) {
            echo "<div class='success'>✓ Maintenance updated successfully</div>";
        } else {
            echo "<div class='error'>✗ Maintenance update returned false</div>";
        }
    } catch (Exception $e) {
        echo "<div class='error'>✗ Maintenance update exception: " . $e->getMessage() . "</div>";
    }
    
    // Test Maintenance DELETE
    echo "<h3>Maintenance DELETE Test</h3>";
    try {
        $deleteResult = $maintenanceModel->delete($maintenanceId, 'schedule');
        if ($deleteResult) {
            echo "<div class='success'>✓ Maintenance deleted successfully</div>";
        } else {
            echo "<div class='error'>✗ Maintenance delete returned false</div>";
        }
    } catch (Exception $e) {
        echo "<div class='error'>✗ Maintenance delete exception: " . $e->getMessage() . "</div>";
    }
}
echo "</div>";

echo "<div class='section'>";
echo "<h2>4. Testing Ticket Model CRUD</h2>";

// Test Ticket CREATE
echo "<h3>Ticket CREATE Test</h3>";
$ticketData = [
    'device_id' => $deviceId,
    'reported_by' => 1,
    'assigned_to' => 1,
    'title' => 'Detailed Test Ticket ' . time(),
    'description' => 'This is a detailed test ticket',
    'priority' => 'medium',
    'status' => 'open'
];

try {
    echo "<div class='info'>Attempting to create ticket with data:</div>";
    echo "<pre>" . print_r($ticketData, true) . "</pre>";
    
    $ticketId = $ticketModel->create($ticketData);
    if ($ticketId) {
        echo "<div class='success'>✓ Ticket created successfully (ID: $ticketId)</div>";
    } else {
        echo "<div class='error'>✗ Ticket create returned false</div>";
    }
} catch (Exception $e) {
    echo "<div class='error'>✗ Ticket create exception: " . $e->getMessage() . "</div>";
    echo "<div class='info'>Stack trace:</div>";
    echo "<pre>" . $e->getTraceAsString() . "</pre>";
}

// Test Ticket READ (if create succeeded)
if (isset($ticketId) && $ticketId) {
    echo "<h3>Ticket READ Test</h3>";
    try {
        $ticket = $ticketModel->getById($ticketId);
        if ($ticket) {
            echo "<div class='success'>✓ Ticket retrieved successfully</div>";
            echo "<div class='info'>Retrieved data:</div>";
            echo "<pre>" . print_r($ticket, true) . "</pre>";
        } else {
            echo "<div class='error'>✗ Ticket read returned false</div>";
        }
    } catch (Exception $e) {
        echo "<div class='error'>✗ Ticket read exception: " . $e->getMessage() . "</div>";
    }
    
    // Test Ticket UPDATE
    echo "<h3>Ticket UPDATE Test</h3>";
    try {
        $updateData = $ticketData;
        $updateData['title'] = 'Updated Detailed Test Ticket ' . time();
        
        $updateResult = $ticketModel->update($ticketId, $updateData);
        if ($updateResult) {
            echo "<div class='success'>✓ Ticket updated successfully</div>";
        } else {
            echo "<div class='error'>✗ Ticket update returned false</div>";
        }
    } catch (Exception $e) {
        echo "<div class='error'>✗ Ticket update exception: " . $e->getMessage() . "</div>";
    }
    
    // Test Ticket DELETE
    echo "<h3>Ticket DELETE Test</h3>";
    try {
        $deleteResult = $ticketModel->delete($ticketId);
        if ($deleteResult) {
            echo "<div class='success'>✓ Ticket deleted successfully</div>";
        } else {
            echo "<div class='error'>✗ Ticket delete returned false</div>";
        }
    } catch (Exception $e) {
        echo "<div class='error'>✗ Ticket delete exception: " . $e->getMessage() . "</div>";
    }
}
echo "</div>";

echo "<div class='section'>";
echo "<h2>5. Summary</h2>";
echo "<div class='info'>This detailed test should help identify the specific issues with the Maintenance and Ticket models.</div>";
echo "<div class='info'>Check the error messages and stack traces above for detailed debugging information.</div>";
echo "</div>";
?>
